import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';

import 'package:water_metering/features/auth/bloc/auth_bloc.dart';
import 'package:water_metering/features/auth/bloc/auth_event.dart';
import 'package:water_metering/features/auth/bloc/auth_state.dart';

void main() {
  group('AuthBloc State Management Tests', () {
    late AuthBloc authBloc;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      authBloc = AuthBloc();
    });

    tearDown(() {
      authBloc.close();
    });

    test('initial state is AuthInitial', () {
      expect(authBloc.state, equals(const AuthInitial()));
    });

    group('AuthCheckRequested', () {
      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoading] and then either AuthAuthenticated or AuthUnauthenticated',
        build: () => authBloc,
        act: (bloc) => bloc.add(const AuthCheckRequested()),
        verify: (bloc) {
          // Verify that AuthLoading was emitted first
          expect(bloc.state, isA<AuthState>());
        },
      );
    });

    group('AuthLoginRequested', () {
      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoading] and then AuthError when login fails',
        build: () => authBloc,
        act: (bloc) => bloc.add(const AuthLoginRequested(
          email: '<EMAIL>',
          password: 'wrongpassword',
        )),
        verify: (bloc) {
          // Verify that the final state is an error state
          expect(bloc.state, isA<AuthError>());
        },
      );
    });

    group('AuthLogoutRequested', () {
      blocTest<AuthBloc, AuthState>(
        'emits [AuthLoggingOut] when logout is requested',
        build: () => authBloc,
        act: (bloc) => bloc.add(const AuthLogoutRequested()),
        verify: (bloc) {
          // Verify that logout process was initiated
          expect(bloc.state, isA<AuthState>());
        },
      );
    });

    group('AuthTokenRefreshRequested', () {
      blocTest<AuthBloc, AuthState>(
        'emits [AuthTokenRefreshing] when token refresh is requested',
        build: () => authBloc,
        act: (bloc) => bloc.add(const AuthTokenRefreshRequested()),
        verify: (bloc) {
          // Verify that token refresh was attempted
          expect(bloc.state, isA<AuthState>());
        },
      );
    });

    group('State Transitions', () {
      test('AuthLoading state has correct properties', () {
        const state = AuthLoading();
        expect(state.props, isEmpty);
      });

      test('AuthError state has correct properties', () {
        const state = AuthError(message: 'Test error');
        expect(state.props.length, equals(2)); // message and userProfile
        expect(state.message, equals('Test error'));
      });

      test('AuthAuthenticated state has correct properties', () {
        const state = AuthAuthenticated(
          accessToken: 'token',
          userEmail: '<EMAIL>',
          userProfile: {'name': 'Test User'},
        );
        expect(
            state.props,
            equals([
              'token',
              '<EMAIL>',
              {'name': 'Test User'}
            ]));
        expect(state.accessToken, equals('token'));
        expect(state.userEmail, equals('<EMAIL>'));
        expect(state.userProfile, equals({'name': 'Test User'}));
      });

      test('AuthTwoFactorRequired state has correct properties', () {
        const state = AuthTwoFactorRequired(referenceCode: 'ref123');
        expect(state.props, equals(['ref123']));
        expect(state.referenceCode, equals('ref123'));
      });
    });
  });
}
