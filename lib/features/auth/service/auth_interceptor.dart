import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';
import 'auth_service.dart';

/// HTTP Interceptor for automatic token refresh and authentication
/// Integrates with AuthBloc for centralized authentication state management
class AuthInterceptor extends Interceptor {
  final AuthBloc authBloc;

  AuthInterceptor({required this.authBloc});

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    try {
      // Add access token to requests that need authentication
      if (_requiresAuth(options.path)) {
        final accessToken = await AuthService.getAccessToken();
        if (accessToken != null) {
          options.headers['Authorization'] = 'Bearer $accessToken';
        }
      }

      // Add common headers
      options.headers['medium'] = 'phone';
      options.headers['Content-Type'] = 'text/plain';

      if (kDebugMode) {
        print('Request: ${options.method} ${options.uri}');
        print('Headers: ${options.headers}');
      }

      handler.next(options);
    } catch (e) {
      if (kDebugMode) {
        print('Error in request interceptor: $e');
      }
      handler.reject(DioException(
        requestOptions: options,
        error: e,
        type: DioExceptionType.unknown,
      ));
    }
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      print('Response: ${response.statusCode} ${response.requestOptions.uri}');
    }
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (kDebugMode) {
      print('Error: ${err.response?.statusCode} ${err.requestOptions.uri}');
    }

    // Handle authentication errors
    if (err.response?.statusCode == 401 || err.response?.statusCode == 403) {
      try {
        // Attempt token refresh through BLoC
        authBloc.add(const AuthTokenRefreshRequested());

        // Wait for the refresh to complete
        await for (final state in authBloc.stream) {
          if (state is AuthAuthenticated) {
            // Retry the original request with new token
            final newToken = state.accessToken;
            err.requestOptions.headers['Authorization'] = 'Bearer $newToken';

            try {
              final dio = Dio();
              final response = await dio.fetch(err.requestOptions);
              handler.resolve(response);
              return;
            } catch (retryError) {
              if (kDebugMode) {
                print('Retry failed: $retryError');
              }
              break;
            }
          } else if (state is AuthError || state is AuthUnauthenticated) {
            // Refresh failed, redirect to login
            break;
          }
        }
      } catch (refreshError) {
        if (kDebugMode) {
          print('Token refresh failed: $refreshError');
        }
      }

      // If we reach here, refresh failed - trigger logout
      authBloc.add(const AuthLogoutRequested());
    }

    handler.next(err);
  }

  /// Determines if a request requires authentication
  bool _requiresAuth(String path) {
    // Add paths that require authentication
    const authRequiredPaths = [
      '/au3', // Token check endpoint
      '/dashboard', // Dashboard endpoints
      '/portal', // Portal endpoints
    ];

    return authRequiredPaths.any((authPath) => path.contains(authPath));
  }
}

/// Factory for creating configured Dio instances with auth interceptor
class AuthHttpClient {
  static Dio createClient(AuthBloc authBloc) {
    final dio = Dio();

    // Add auth interceptor
    dio.interceptors.add(AuthInterceptor(authBloc: authBloc));

    // Add logging interceptor in debug mode
    if (kDebugMode) {
      dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: false,
      ));
    }

    // Configure timeouts
    dio.options.connectTimeout = const Duration(seconds: 5);
    dio.options.receiveTimeout = const Duration(seconds: 5);

    return dio;
  }
}
