import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:water_metering/utils/pok.dart';

import '../theme/theme2.dart';

class LoaderUtility {
  static Future<dynamic> showLoader(BuildContext context, Future futureFunction,
      {Color? color}) async {
    // Show the loader
    showDialog(
      context: context,
      barrierDismissible: false,
      // Prevents closing the dialog by tapping outside it
      builder: (BuildContext context) {
        return PopScope(
            canPop: false, // Prevents closing the dialog by back button
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
              child: Container(
                alignment: FractionalOffset.center,
                decoration:
                    BoxDecoration(color: Colors.black.withValues(alpha: 0.5)),
                // Semi-transparent background
                child: SizedBox(
                  width: 75.minSp,
                  height: 75.minSp,
                  child: LoadingAnimationWidget.hexagonDots(
                    size: 75.minSp,
                    color: CommonColors.blue,
                  ),
                  // child: CircularProgressIndicator(
                  //   strokeWidth: 5,
                  //   valueColor:
                  //       AlwaysStoppedAnimation<Color>(color ?? Colors.blue),
                  // ),
                ),
              ),
            ));
      },
    );
    // Wait for the future to complete
    dynamic a;
    try {
      a = await futureFunction;
    } catch (e) {
      // Hide the loader
      Navigator.of(context, rootNavigator: true).pop('dialog');
      rethrow;
    }
    // Hide the loader
    Navigator.of(context, rootNavigator: true).pop('dialog');
    return a;
  }
}

// LoaderUtility.showLoader(
// context,
// LoginPostRequests.signUp(
// _activationCodeController
//     .text,
// _nameController.text,
// _emailController.text,
// selectedCountryCode!
//     .dialCode! +
// _phoneController.text))
//     .then((value) {
// CustomAlert.showCustomscaffoldMessenger(
// context,
// "Verification code sent to your email address and phone number",
// AlertType.info);
// Navigator.of(context).push(
// MaterialPageRoute(
// builder: (context) => SignupPage2(
// code:
// _activationCodeController
//     .text)));
// }).catchError((e) {
// CustomAlert.showCustomscaffoldMessenger(
// context,
// e.toString(),
// AlertType.error);
// return;
// });
// }
